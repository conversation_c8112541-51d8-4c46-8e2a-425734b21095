import { createAgent, getAgentDetail, updateAgent } from '@/api/agent';
import { getEmployees } from '@/api/employee';
import EmployeeCard from '@/components/ai-staff/EmployeeCard';
import KnowledgeListRow from '@/components/ai-staff/KnowledgeListRow';
import Dropdown from '@/components/common/Dropdown';
import TagInput from '@/components/common/TagInput';
import { createConversation } from '@/services/conversation';
import { getKnowledgeList } from '@/services/knowledgeService';
import { formatDateTime } from '@/utils/data-transform';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { useConversations } from '../history/hooks/useConversations';

export interface EmployeeFormValues {
  name: string;
  description: string;
  tags?: string[];
  isPublic?: boolean;
  prompt?: string;
  knowledgeBase?: string;
  knowledgeIds?: Array<{
    id?: string;
    name: string;
    type?: string;
    disabled?: boolean;
    source?: string;
    knowledge_id?: string;
  }> | null;
  createdTime?: string;
  user?: {
    user_name: string;
  };
  agentId?: string; // 新增：传递agentId
  instruction?: string; // 新增：用于渲染提示词
  knowledge_bases?: any[]; // 新增：用于渲染知识库
  agentDescription?: string; // 新增：用于渲染智能体描述
}

interface EmployeePageLayoutProps {
  mode: 'create' | 'edit' | 'preview';
  initialValues?: Partial<EmployeeFormValues>;
  onSubmit?: (values: EmployeeFormValues) => Promise<void>;
  submitText?: string;
  loading?: boolean;
  onBack?: () => void;
  employeeId?: string; // 新增：传递当前员工ID
  agentId?: string; // 新增：传递agentId
}

const EmployeePageLayout: React.FC<EmployeePageLayoutProps> = ({
  mode,
  initialValues = {},
  onSubmit,
  submitText = mode === 'edit'
    ? '保存'
    : mode === 'create'
      ? '创建'
      : undefined,
  loading = false,
  onBack,
  employeeId,
  agentId,
}) => {
  const router = useRouter();
  const { addConversation } = useConversations();
  // ... existing code ...
  // 表单状态
  const [name, setName] = useState(initialValues.name || '');
  const [description, setDescription] = useState(
    initialValues.description || ''
  );
  const [prompt, setPrompt] = useState(initialValues.prompt || '');
  const [selectedTags, setSelectedTags] = useState<string[]>(
    initialValues.tags || []
  );
  const [isPublic, setIsPublic] = useState<string>(
    initialValues.isPublic ? 'public' : 'private'
  );
  const [knowledgeBase, setKnowledgeBase] = useState<string>(
    initialValues.knowledgeBase || ''
  );
  // const [search, setSearch] = useState("");
  // 初始化 selectedKnowledgeIds，合并 initialValues.knowledgeIds 并去重
  // mergeKnowledgeIds参数保证为数组类型，不能为null
  const mergeKnowledgeIds = (a?: Array<any>, b?: Array<any>) => {
    const arrA = Array.isArray(a) ? a : [];
    const arrB = Array.isArray(b) ? b : [];
    console.log('mergeKnowledgeIds 输入数据 a:', arrA);
    console.log('mergeKnowledgeIds 输入数据 b:', arrB);

    const map = new Map();
    [...arrA, ...arrB].forEach(item => {
      console.log('处理项目:', item);
      // 处理不同的数据格式，优先使用 id，其次使用 knowledge_id，最后使用 name
      const key = item.id || item.knowledge_id || item.name;
      if (key) {
        // 标准化数据格式
        const standardizedItem = {
          id: item.id || item.knowledge_id || item.name,
          name: item.name,
          type: item.type || 'document',
          disabled: item.disabled || false,
          source: item.source || 'ragflow',
        };
        console.log('标准化后的项目:', standardizedItem);
        map.set(key, standardizedItem);
      }
    });
    const result = Array.from(map.values());
    console.log('mergeKnowledgeIds 最终结果:', result);
    return result;
  };
  const [selectedKnowledgeIds, setSelectedKnowledgeIds] = useState<
    Array<{
      name: string;
      type: string;
      disabled: boolean;
      id: string;
      source: string;
    }>
  >(
    mergeKnowledgeIds(
      Array.isArray(initialValues.knowledgeIds)
        ? initialValues.knowledgeIds
        : [],
      []
    )
  );

  // initialValues 变化时同步 selectedKnowledgeIds
  useEffect(() => {
    console.log('initialValues 变化:', initialValues);
    console.log('initialValues.knowledgeIds:', initialValues.knowledgeIds);

    setName(initialValues.name || '');
    setDescription(initialValues.description || '');
    setPrompt(initialValues.prompt || '');
    setSelectedTags(initialValues.tags || []);
    setIsPublic(initialValues.isPublic ? 'public' : 'private');

    const mergedKnowledgeIds = mergeKnowledgeIds(
      Array.isArray(initialValues.knowledgeIds)
        ? initialValues.knowledgeIds
        : [],
      []
    );
    console.log('合并后的知识库数据:', mergedKnowledgeIds);
    setSelectedKnowledgeIds(mergedKnowledgeIds);
  }, [initialValues]);

  // useEffect: initialValues 变化时同步 abilityName（编辑模式下自动赋值）
  useEffect(() => {
    setName(initialValues.name || '');
    setDescription(initialValues.description || '');
    setPrompt(initialValues.prompt || '');
    setSelectedTags(initialValues.tags || []);
    setIsPublic(initialValues.isPublic ? 'public' : 'private');
    setSelectedKnowledgeIds(
      mergeKnowledgeIds(
        Array.isArray(initialValues.knowledgeIds)
          ? initialValues.knowledgeIds
          : [],
        []
      )
    );
    if (mode === 'edit' && initialValues.name) {
      setAbilityName(initialValues.name);
    }
  }, [initialValues, mode]);

  // 1. 新增能力列表数据
  const [abilityList, setAbilityList] = useState<any[]>([]);
  // 2. 获取能力数据（用 getEmployees 接口）
  useEffect(() => {
    getEmployees({
      IsPublic: true,
      Disabled: false,
      'Pager.Page': 1,
      'Pager.Size': 100,
      ExcludePersonalCreated: false,
    }).then(res => {
      setAbilityList(res.items || []);
    });
  }, []);

  // 字数统计
  const descriptionCount = description.length;
  const promptCount = prompt.length;

  // 锚点导航相关
  const [activeAnchor, setActiveAnchor] = useState('base');
  const baseRef = useRef<HTMLDivElement>(null);
  const detailRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // ... existing code ...
  // const handleSearchKeyPress = async (e: React.KeyboardEvent) => {
  //   if (e.key === "Enter" && !e.shiftKey) {
  //     e.preventDefault();
  //   }
  // };
  // const handleClear = () => setSearch("");

  useEffect(() => {
    const handleScroll = () => {
      if (!baseRef.current || !detailRef.current || !contentRef.current) return;
      const detailTop =
        detailRef.current.getBoundingClientRect().top -
        contentRef.current.getBoundingClientRect().top;
      const scrollTop = contentRef.current.scrollTop;
      if (scrollTop + 10 >= detailTop) {
        setActiveAnchor('detail');
      } else {
        setActiveAnchor('base');
      }
    };
    const el = contentRef.current;
    if (el) {
      el.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (el) el.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const scrollToAnchor = (anchor: 'base' | 'detail') => {
    const ref = anchor === 'base' ? baseRef : detailRef;
    if (ref.current && contentRef.current) {
      ref.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
    // setActiveAnchor(anchor); // 移除
  };

  const handleTagChange = (value: string | string[]) => {
    if (Array.isArray(value)) {
      setSelectedTags(value);
    }
  };

  const promptRef = useRef<HTMLTextAreaElement>(null);
  const handlePromptInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value);
    const textarea = promptRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.max(150, textarea.scrollHeight) + 'px';
    }
  };

  // 知识库选择弹窗相关
  const [kbModalOpen, setKbModalOpen] = useState(false);
  // 员工能力选择弹窗
  const [abilityModalOpen, setAbilityModalOpen] = useState(false);
  // ability search state
  const [abilitySearch, setAbilitySearch] = useState('');

  // 临时变量：能力选择与命名
  const [tempAbility, setTempAbility] = useState<any>(null);
  const [tempAbilityName, setTempAbilityName] = useState('');
  const [tempKnowledgeBase, setTempKnowledgeBase] = useState('');

  // 新增 abilityNamingModalOpen 和 selectedAbility 状态
  const [abilityNamingModalOpen, setAbilityNamingModalOpen] = useState(false);
  const [selectedAbility, setSelectedAbility] = useState<any>(null);
  const [abilityName, setAbilityName] = useState('');
  const [selectedAbilityName, setSelectedAbilityName] = useState('');
  // 新增知识库列表和搜索状态
  const [knowledgeList, setKnowledgeList] = useState<any[]>([]);
  const [knowledgeSearch, setKnowledgeSearch] = useState('');

  // 获取知识库列表
  useEffect(() => {
    if (!kbModalOpen) return;
    getKnowledgeList({
      keywords: knowledgeSearch,
      page: 1,
      pageSize: 10,
    })
      .then((response: any) => {
        setKnowledgeList(response.data?.kbs || []);
      })
      .catch((error: any) => {
        console.error('获取知识库列表失败:', error);
        setKnowledgeList([]);
      });
  }, [kbModalOpen, knowledgeSearch]);

  // 提交
  const handleSubmit = async () => {
    if (!name.trim()) {
      toast.error('请输入员工名称');
      return;
    }
    if (!abilityName.trim()) {
      toast.error('请输入能力名称');
      return;
    }
    try {
      // 构造知识库参数，包含name、description、type
      const knowledgeBases = selectedKnowledgeIds.map(kb => ({
        name: kb.name,
        disabled: false,
        type: 'document',
        knowledge_id: kb.id,
        source: 'ragflow',
      }));

      if (mode === 'edit' && initialValues.agentId) {
        // 编辑模式：获取详情数据的所有字段，用表单数据覆盖相同字段
        const agentDetail = await getAgentDetail(initialValues.agentId);

        // 表单数据覆盖相同字段
        const formData = {
          name: abilityName,
          instruction: prompt,
          knowledge_bases: knowledgeBases,
        };

        // 详情数据 + 表单数据覆盖，不使用默认值
        const agentParams = {
          ...agentDetail, // 详情数据的所有字段
          ...formData, // 表单数据覆盖相同字段
        };

        await updateAgent(initialValues.agentId, agentParams);
        await onSubmit?.({
          name: name.trim(),
          description: description.trim(),
          tags: selectedTags,
          isPublic: isPublic === 'public',
          agentId: initialValues.agentId,
        });
        toast.success('保存成功');
        router.push('/ai-staff');
        return;
      }
      // 创建模式逻辑
      let agentParams;
      // 如果复用已有能力，获取其详情数据的所有字段
      if (knowledgeBase) {
        try {
          const agentDetail = await getAgentDetail(knowledgeBase);

          // 表单数据覆盖相同字段
          const formData = {
            name: abilityName.trim(),
            instruction: prompt,
            knowledge_bases: knowledgeBases,
          };

          // 详情数据 + 表单数据覆盖，不使用默认值
          agentParams = {
            ...agentDetail, // 详情数据的所有字段
            ...formData, // 表单数据覆盖相同字段
          };
        } catch {
          // 如果获取详情失败，抛出错误让用户知道
          toast.error('获取能力详情失败，请重新选择');
          return;
        }
      } else {
        // 没有复用能力，只使用表单数据，不使用默认模板
        agentParams = {
          name: abilityName.trim(),
          instruction: prompt,
          knowledge_bases: knowledgeBases,
        };
      }
      const agentRes = await createAgent(agentParams);
      const agentId = agentRes?.agent_id;
      if (!agentId) {
        toast.error('创建智能体失败');
        return;
      }
      // 2. 创建员工，只传接口需要的字段
      await onSubmit?.({
        name: name.trim(),
        description: description.trim(),
        tags: selectedTags,
        isPublic: isPublic === 'public',
        agentId,
      });
      toast.success('创建员工成功');
      router.push('/ai-staff');
    } catch (error) {
      toast.error(
        mode === 'edit' ? '保存失败，请稍后重试' : '创建员工失败，请稍后重试'
      );
    }
  };

  const handleBack = () => {
    if (onBack) onBack();
    else router.push('/ai-staff');
  };

  const handleOpenKnowledgeModal = () => {
    setKbModalOpen(true);
  };

  return (
    <div className="h-full min-h-0 bg-[#FAFAFA] flex flex-col px-6">
      {/* 头部区域 */}
      <div className="h-[53px] w-full flex items-center border-b border-[#0000000F] justify-between">
        <img
          src="/back.svg"
          alt="back"
          className="cursor-pointer mt-[2.5px]"
          onClick={handleBack}
        />
        {/* 右上角创建会话按钮，仅编辑模式且有agentId时显示 */}
        {(mode === 'edit' || mode === 'preview') && agentId && (
          <button
            className="bg-[#000000E0] text-[#FFFFFF] font-medium rounded-[8px] text-[14px] px-3 py-2 flex items-center justify-center cursor-pointer hover:bg-[#000000F0]"
            onClick={async () => {
              try {
                const conv = await createConversation(agentId);
                if (conv && conv.id) {
                  addConversation(conv);
                  router.push(`/history/${conv.id}`);
                } else {
                  toast.error('创建会话失败');
                }
              } catch {
                toast.error('创建会话失败');
              }
            }}
          >
            <img
              src="/assets/ai-employees/add.svg"
              alt="add"
              className="mr-2"
            />
            创建会话
          </button>
        )}
      </div>
      {/* 主体区域：左右布局 */}
      <div className="flex-1 min-h-0 flex items-start">
        {/* 左侧锚点导航栏 */}
        <div className="w-[236px] bg-[#FAFAFA] py-6 ">
          <nav className="flex flex-col gap-2 w-full">
            <button
              className={`text-left py-2 px-3 text-[14px] rounded-[8px] transition-colors font-medium ${
                activeAnchor === 'base'
                  ? 'bg-[#0000000A] text-[#000000B8]'
                  : 'text-[#00000066] hover:bg-[#F7F7F7]'
              }`}
              onClick={() => scrollToAnchor('base')}
            >
              基础信息
            </button>
            <button
              className={`text-left py-2 px-3  text-[14px] rounded-[8px] transition-colors font-medium ${
                activeAnchor === 'detail'
                  ? 'bg-[#0000000A] text-[#000000B8]'
                  : 'text-[#00000066] hover:bg-[#F7F7F7]'
              }`}
              onClick={() => scrollToAnchor('detail')}
            >
              详细配置
            </button>
          </nav>
        </div>
        {/* 右侧内容区（可滚动） */}
        <div
          className="flex-1 min-h-0 flex flex-col px-[80px] py-6 overflow-y-auto overflow-x-hidden"
          ref={contentRef}
          style={{
            scrollBehavior: 'smooth',
            maxHeight: 'calc(100vh - 53px - 77px - 40px)',
          }}
        >
          <div className="w-full">
            {/* 基础信息 */}
            <div
              ref={baseRef}
              className="pb-[40px] border-b border-[#0000000F] mb-[40px] flex flex-col gap-4"
            >
              {/* <span className="text-[20px] font-medium text-[#000000B8]">
                基础信息
              </span> */}
              {mode === 'preview' ? (
                <div className="flex items-start gap-8">
                  <div className="flex flex-col gap-2 flex-1 min-w-0">
                    <div className="text-[20px] font-medium text-[#000000E0] leading-tight mb-1 truncate">
                      {initialValues.name}
                    </div>
                    <div className="text-[16px] text-[#000000B8] leading-normal">
                      {initialValues.description}
                    </div>
                    <div className="flex flex-wrap gap-2 ">
                      {Array.isArray(initialValues.tags) &&
                        initialValues.tags.map(tag => (
                          <span
                            key={tag}
                            className="bg-[#FAFAFA] text-[#000000B8] rounded-[6px] px-[10px] py-1 text-[14px] font-medium border border-[#0000000F]"
                          >
                            {tag}
                          </span>
                        ))}
                    </div>
                    <div className="flex items-start gap-1 mt-2 text-[14px] text-[#888] flex-col justify-start">
                      <span className="text-[14px] text-[#00000066] font-normal">
                        {initialValues.user?.user_name}
                      </span>
                      {(initialValues as any).createdTime && (
                        <span className="text-[14px] text-[#00000066] font-normal">
                          创建时间：{formatDateTime(initialValues.createdTime)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col gap-4">
                  {/* 名称 */}
                  <div>
                    <label className="block text-[13px] text-[#000000B8] font-medium mb-1">
                      名称
                    </label>
                    <input
                      type="text"
                      value={name}
                      onChange={e => setName(e.target.value)}
                      placeholder="请输入"
                      className="bg-[#F5F5F5] w-full h-[40px] px-3 py-2 border border-[#0000000F] rounded-[8px] text-[14px] text-[#000000B8] outline-none focus:border-[#0000001F]"
                    />
                  </div>
                  {/* 描述 */}
                  <div>
                    <label className="block text-[13px] text-[#000000B8] font-medium mb-1">
                      描述
                    </label>
                    <div className="relative">
                      <textarea
                        value={description}
                        onChange={e => setDescription(e.target.value)}
                        placeholder="请输入"
                        maxLength={100}
                        className="bg-[#F5F5F5] w-full h-[80px] px-3 py-2 border border-[#0000000F] rounded-[8px] text-[14px] text-[#000000B8] outline-none focus:border-[#0000001F] resize-none"
                      />
                      <div className="h-[22px] absolute py-[2px] px-[6px] bottom-[12px] left-[8px] text-[12px] text-[#00000066] bg-[#EBEBEB] rounded-[4px] font-normal">
                        {descriptionCount}/100
                      </div>
                    </div>
                  </div>
                  {/* 标签 */}
                  <div>
                    <label className="block text-[13px] text-[#000000B8] font-medium mb-1">
                      标签
                    </label>
                    <TagInput tags={selectedTags} onChange={handleTagChange} />
                  </div>
                  {/* 权限 */}
                  <div>
                    <label className="block text-[13px] text-[#000000B8] font-medium mb-1">
                      权限
                    </label>
                    <Dropdown
                      options={[
                        { label: '公开', value: 'public' },
                        { label: '个人', value: 'private' },
                      ]}
                      value={isPublic}
                      onChange={v => setIsPublic(v as string)}
                      placeholder="请选择"
                      showClearButton={false}
                      className="w-[200px]"
                    />
                  </div>
                </div>
              )}
            </div>
            {/* 详细配置 */}
            <div ref={detailRef} className="mb-8 flex flex-col gap-4">
              <span className="text-[20px] font-medium text-[#000000B8]">
                详细配置
              </span>
              {/* 提示词 */}
              <div>
                <label className="block text-[13px] text-[#000000B8] font-medium mb-1">
                  提示词
                </label>
                <div className="relative">
                  <textarea
                    ref={promptRef}
                    value={prompt}
                    onChange={handlePromptInput}
                    placeholder="请输入"
                    maxLength={1000}
                    style={{
                      minHeight: 150,
                      maxHeight: 300,
                      resize: 'vertical',
                      overflowY: 'auto',
                    }}
                    className="bg-[#F5F5F5] w-full px-3 py-2 border border-[#0000000F] rounded-[8px] text-[14px] text-[#000000B8] outline-none focus:border-[#0000001F]"
                    readOnly={mode === 'preview'}
                  />
                  <div className="h-[22px] absolute py-[2px] px-[6px] bottom-[12px] left-[8px] text-[12px] text-[#00000066] bg-[#EBEBEB] rounded-[4px] font-normal">
                    {promptCount}/1000
                  </div>
                </div>
              </div>
              {/* 员工能力 */}
              <div>
                <label className="block text-[13px] text-[#000000B8] font-medium mb-1">
                  员工能力
                </label>
                {mode === 'create' ? (
                  <div
                    className="flex items-center bg-[#F5F5F5] w-full min-h-[40px] px-3 py-2 border border-[#0000000F] rounded-[8px] text-[14px] text-[#000000B8] cursor-pointer justify-between"
                    onClick={() => setAbilityModalOpen(true)}
                  >
                    <span>{abilityName || '请选择能力'}</span>
                    <img
                      src="/assets/ai-employees/select.svg"
                      alt="select"
                      className="cursor-pointer"
                    />
                  </div>
                ) : (
                  <div className="bg-[#F5F5F5] w-full min-h-[40px] px-3 py-2 border border-[#0000000F] rounded-[8px] text-[14px] text-[#000000B8]">
                    {initialValues.name || '-'}
                  </div>
                )}
              </div>
              {/* 知识库 */}
              <div className="flex flex-col">
                <div className="flex items-center justify-between">
                  <label className="block text-[13px] text-[#000000B8] font-medium mb-1">
                    知识库
                  </label>
                  {mode !== 'preview' && (
                    <button
                      className="text-[14px] text-[#000000B8] border border border-[#0000000F] rounded-[8px] px-4 py-2 hover:bg-[#0000000A] cursor-pointer"
                      onClick={() => setKbModalOpen(true)}
                    >
                      添加
                    </button>
                  )}
                </div>
                {/* 已选知识库展示 */}
                {selectedKnowledgeIds.length > 0 ? (
                  <div className="mt-1 border border-[#0000000F] p-[6px] rounded-[8px] flex flex-col gap-1 bg-[#00000005]">
                    {selectedKnowledgeIds.map((kb, idx) => (
                      <KnowledgeListRow
                        key={kb.id}
                        index={idx}
                        kb={kb}
                        {...(mode !== 'preview'
                          ? {
                              onRemove: () => {
                                setSelectedKnowledgeIds(ids =>
                                  ids.filter(id => id.id !== kb.id)
                                );
                              },
                            }
                          : {})}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="mt-1 border border-[#0000000F] text-[#000000B8] text-[13px] p-[6px] rounded-[8px] flex flex-col gap-1 bg-[#00000005]">
                    暂无关联数据库
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* 底部按钮：预览模式不显示 */}
      {mode !== 'preview' && (
        <div className="h-[77px] px-6 flex items-center justify-end border-t border-[#0000000F] bg-[#FAFAFA] z-10">
          <button
            onClick={handleBack}
            className="cursor-pointer mr-2 px-4 py-2 border border-[#0000000F] font-medium rounded-[8px] text-[14px] text-[#000000B8] hover:bg-[#0000000A]"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            className="cursor-pointer px-4 py-2 bg-[#000000E0] font-medium text-white rounded-[8px] text-[14px] hover:bg-[#000000F0]"
            disabled={loading}
          >
            {submitText}
          </button>
        </div>
      )}
      {/* 知识库选择弹窗 */}
      {kbModalOpen && mode !== 'preview' && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#00000080]">
          <div className="bg-white rounded-[16px] w-[75%] h-[60%] shadow-lg p-8 flex flex-col">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center gap-2">
                <span className="text-[18px] font-medium text-[#000000E0]">
                  选择库
                </span>
                <div className="w-[1px] bg-[#0000000F] h-[24px]"></div>
                <div className="w-[200px]">
                  <div className="flex items-center bg-[#FFFFFF] h-[37px] w-full px-3  rounded-[8px]">
                    <img
                      src="/assets/search/search.svg"
                      alt="search"
                      className="w-4 h-4"
                    />
                    <input
                      className="flex-1 min-w-0 h-full px-2 bg-transparent font-normal outline-none text-sm text-[#000000B8] placeholder:text-[#00000066]"
                      type="text"
                      value={knowledgeSearch}
                      onChange={e => setKnowledgeSearch(e.target.value)}
                      placeholder="搜索"
                    />
                    {knowledgeSearch && (
                      <button
                        type="button"
                        className="ml-1 focus:outline-none cursor-pointer hover:bg-[#00000014] rounded-full p-0.5 transition-colors"
                        onClick={() => setKnowledgeSearch('')}
                        tabIndex={-1}
                      >
                        <img
                          src="/assets/search/clean.svg"
                          alt="clear"
                          className="w-4 h-4"
                        />
                      </button>
                    )}
                  </div>
                </div>
              </div>
              <img
                src="/close.svg"
                alt="close"
                onClick={() => setKbModalOpen(false)}
                className="cursor-pointer"
              />
            </div>
            <div className="grid grid-cols-2 gap-6 overflow-y-auto border-t border-[#0000000F] pt-4">
              {knowledgeList.map(kb => {
                const isSelected = selectedKnowledgeIds.some(
                  item => item.id === kb.id
                );
                return (
                  <div
                    key={kb.id}
                    className="group flex flex-col justify-between bg-white rounded-[8px] border border-[#0000000F] p-3 mb-2 transition-all duration-200 hover:shadow"
                    style={{ minHeight: 150 }}
                  >
                    {/* 标题和标签 */}
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-[#000000E0] text-[16px]">
                          {kb.display_name || kb.name}
                        </span>
                      </div>
                      <div className="text-[#000000B8] text-[13px] font-normal line-clamp-2 mb-1">
                        {kb.description}
                      </div>
                      <div className="flex flex-wrap gap-1 mb-1">
                        {Array.isArray(kb.labels) &&
                          kb.labels.map((label: any) => (
                            <span
                              key={label}
                              className="bg-[#FAFAFA] text-[#4285F4] rounded-[4px] px-2 py-[2px] text-[12px] border border-[#4285F429]"
                            >
                              {label}
                            </span>
                          ))}
                      </div>
                    </div>
                    {/* footer：未悬浮时显示文档数，无按钮 */}
                    <div className="flex justify-between items-end mt-2 group-hover:hidden">
                      <span className="text-[12px] text-[#00000066] font-normal">
                        {`文档数：${kb.count}`}
                      </span>
                      <span className="w-[80px]" />
                    </div>
                    {/* 悬浮footer：显示创建时间和按钮 */}
                    <div className="hidden group-hover:flex justify-between items-end mt-2">
                      <span className="text-[12px] text-[#00000066] font-normal">
                        {kb.created_time
                          ? `创建时间：${formatDateTime(kb.created_time)}`
                          : ''}
                      </span>
                      {isSelected ? (
                        <button
                          className="cursor-not-allowed font-medium h-[32px] px-4 py-1 bg-[#0000000A] text-[#000000B8] text-[14px] rounded-[8px] hidden group-hover:inline-flex"
                          disabled
                        >
                          已添加
                        </button>
                      ) : (
                        <button
                          className="cursor-pointer h-[32px] px-4 py-1 bg-[#000000E0] text-white text-[14px] rounded-[8px] hover:bg-[#000000F0] transition-colors hidden group-hover:inline-flex"
                          onClick={() =>
                            setSelectedKnowledgeIds(ids =>
                              mergeKnowledgeIds(ids, [{ id: kb.id, ...kb }])
                            )
                          }
                        >
                          添加
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
      {/* 员工能力选择弹窗 */}
      {mode === 'create' && abilityModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#00000080]">
          <div className="bg-white rounded-[16px] w-[75%] h-[60%] shadow-lg p-8 flex flex-col">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center gap-2">
                <span className="text-[18px] font-medium text-[#000000E0]">
                  选择员工能力
                </span>
                <div className="w-[1px] bg-[#0000000F] h-[24px]"></div>
                <div className="w-[200px]">
                  <div className="flex items-center bg-[#FFFFFF] h-[37px] w-full px-3  rounded-[8px]">
                    <img
                      src="/assets/search/search.svg"
                      alt="search"
                      className="w-4 h-4"
                    />
                    <input
                      className="flex-1 min-w-0 h-full px-2 bg-transparent font-normal outline-none text-sm text-[#000000B8] placeholder:text-[#00000066]"
                      type="text"
                      value={abilitySearch}
                      onChange={e => setAbilitySearch(e.target.value)}
                      placeholder="搜索"
                    />
                    {abilitySearch && (
                      <button
                        type="button"
                        className="ml-1 focus:outline-none cursor-pointer hover:bg-[#00000014] rounded-full p-0.5 transition-colors"
                        onClick={() => setAbilitySearch('')}
                        tabIndex={-1}
                      >
                        <img
                          src="/assets/search/clean.svg"
                          alt="clear"
                          className="w-4 h-4"
                        />
                      </button>
                    )}
                  </div>
                </div>
              </div>
              <img
                src="/close.svg"
                alt="close"
                onClick={() => setAbilityModalOpen(false)}
                className="cursor-pointer"
              />
            </div>
            <div className="grid grid-cols-2 gap-6 overflow-y-auto border-t border-[#0000000F] pt-4">
              {abilityList.length === 0 ? (
                <div className="col-span-2 flex justify-center items-center h-40 text-[#888] text-lg">
                  <div
                    className="w-full"
                    style={{ cursor: 'pointer' }}
                    onClick={async () => {
                      // 拉取模板agent详细信息
                      try {
                        const agentDetail = await getAgentDetail(
                          'ca6b12bc-f164-4814-94a3-0a6ae02ae9cd'
                        );
                        setTempAbility(agentDetail);
                        setTempKnowledgeBase(
                          agentDetail.agent_id ||
                            'ca6b12bc-f164-4814-94a3-0a6ae02ae9cd'
                        );
                        setTempAbilityName('');
                        setAbilityModalOpen(false);
                        setAbilityNamingModalOpen(true);
                      } catch {
                        toast.error('获取默认能力模板失败');
                      }
                    }}
                  >
                    <EmployeeCard
                      employee={{
                        id: 'ca6b12bc-f164-4814-94a3-0a6ae02ae9cd',
                        name: '默认能力模板',
                        description: '默认能力模板',
                        avatar: '/assets/ai-employees/AI.svg',
                        role: '能力',
                        tags: [],
                        disabled: false,
                        createdTime: '',
                      }}
                      className="hover:shadow-none hover:bg-[#00000005] transition-all duration-200 h-[100px]"
                      minimal
                    />
                  </div>
                </div>
              ) : (
                abilityList
                  .filter(item => item && item.agent)
                  .filter(
                    item =>
                      item.agent.name.includes(abilitySearch) ||
                      item.agent.description.includes(abilitySearch)
                  )
                  .map(item => {
                    const isSelected = knowledgeBase === item.agent.agent_id;
                    return (
                      <EmployeeCard
                        key={item.agent.agent_id}
                        employee={{
                          id: item.agent.agent_id,
                          name: item.agent.name,
                          description: item.agent.description,
                          avatar:
                            item.agent.icon_url ||
                            '/assets/ai-employees/AI.svg',
                          role: '能力',
                          tags: [], // 能力没有 tags 字段
                          disabled: false, // 能力没有 disabled 字段
                          createdTime: item.agent.created_time || '', // 能力没有 createdTime 字段
                        }}
                        className="hover:shadow-none hover:bg-[#00000005] transition-all duration-200 h-[100px]"
                        minimal
                        footerRight={
                          isSelected ? (
                            <button
                              className="cursor-not-allowed font-medium hidden group-hover:inline-flex items-center h-[37px] px-4 py-2 bg-[#0000000A] text-[#000000B8] text-[14px] rounded-[8px]"
                              disabled
                            >
                              当前能力
                            </button>
                          ) : (
                            <button
                              className="cursor-pointer hidden group-hover:inline-flex items-center h-[37px] px-4 py-2 bg-[#000000E0] text-white text-[14px] rounded-[8px] hover:bg-[#000000F0] transition-colors"
                              onClick={e => {
                                e.stopPropagation();
                                setTempAbility(item);
                                setTempKnowledgeBase(item.agent.agent_id);
                                setTempAbilityName('');
                                setAbilityModalOpen(false);
                                setAbilityNamingModalOpen(true);
                              }}
                            >
                              复用
                            </button>
                          )
                        }
                      />
                    );
                  })
              )}
            </div>
          </div>
        </div>
      )}
      {abilityNamingModalOpen && tempAbility && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#00000080]">
          <div className="bg-white rounded-[16px] w-[560px] shadow-lg p-8 flex flex-col items-center">
            <div className="w-full flex justify-between items-center mb-6">
              <div className="flex items-center gap-2">
                <img
                  src="/back.svg"
                  alt="back"
                  className="cursor-pointer"
                  onClick={() => {
                    setAbilityNamingModalOpen(false);
                    setAbilityModalOpen(true);
                    setTempAbility(null);
                    setTempAbilityName('');
                    setTempKnowledgeBase('');
                  }}
                />
                <span className="text-[18px] font-medium text-[#000000E0]">
                  能力命名
                </span>
              </div>
              <img
                src="/close.svg"
                alt="close"
                onClick={() => {
                  setAbilityNamingModalOpen(false);
                  setTempAbility(null);
                  setTempAbilityName('');
                  setTempKnowledgeBase('');
                }}
                className="cursor-pointer"
              />
            </div>
            <EmployeeCard
              employee={tempAbility}
              minimal
              className="w-full mb-6"
            />
            {/* 仅非编辑模式下显示能力名称输入框，编辑模式下显示只读文本 */}
            <div className="w-full mb-6">
              <label className="block text-[14px] text-[#000000B8] font-medium mb-2">
                名称
              </label>
              {mode === 'edit' ? (
                <div className="bg-[#F5F5F5] w-full h-[40px] px-3 py-2 border border-[#0000000F] rounded-[8px] text-[14px] text-[#000000B8] flex items-center">
                  {abilityName}
                </div>
              ) : (
                <>
                  <input
                    type="text"
                    value={tempAbilityName}
                    onChange={e => {
                      if (e.target.value.length <= 10)
                        setTempAbilityName(e.target.value);
                    }}
                    placeholder="请输入"
                    className="bg-[#F5F5F5] w-full h-[40px] px-3 py-2 border border-[#0000000F] rounded-[8px] text-[14px] text-[#000000B8] outline-none focus:border-[#0000001F]"
                  />
                  <div className="text-right text-[12px] text-[#00000066] mt-1">
                    {tempAbilityName.length}/10
                  </div>
                </>
              )}
            </div>
            <div className="w-full flex justify-end gap-2">
              <button
                onClick={() => {
                  setAbilityNamingModalOpen(false);
                  setTempAbility(null);
                  setTempAbilityName('');
                  setTempKnowledgeBase('');
                }}
                className="cursor-pointer px-4 py-2 border border-[#0000000F] font-medium rounded-[8px] text-[14px] text-[#000000B8] hover:bg-[#0000000A]"
              >
                取消
              </button>
              <button
                onClick={async () => {
                  if (!tempAbilityName.trim()) return;
                  setAbilityNamingModalOpen(false);
                  setSelectedAbility(tempAbility);
                  setAbilityName(tempAbilityName.trim());
                  setKnowledgeBase(tempKnowledgeBase);
                  setTempAbility(null);
                  setTempAbilityName('');
                  setTempKnowledgeBase('');
                  try {
                    const agentInfo = await getAgentDetail(tempKnowledgeBase);
                    setPrompt(agentInfo.instruction || '');
                    setSelectedKnowledgeIds(
                      Array.isArray(agentInfo.knowledge_bases)
                        ? agentInfo.knowledge_bases.map((kb: any) => ({
                            name: kb.name,
                            ...kb,
                          }))
                        : []
                    );
                  } catch (e) {
                    toast.error('获取智能体信息失败');
                  }
                }}
                className="cursor-pointer px-4 py-2 bg-[#000000E0] font-medium text-white rounded-[8px] text-[14px] hover:bg-[#000000F0]"
                disabled={!tempAbilityName.trim()}
              >
                确定
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeePageLayout;
